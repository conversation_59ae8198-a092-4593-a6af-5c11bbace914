import 'package:dio/dio.dart';
import '../models/member.dart';

class ApiRepository {
  final Dio _dio;
  static const String _baseUrl = 'https://anaconda2121.pythonanywhere.com/api/';

  ApiRepository() : _dio = Dio() {
    _dio.options.baseUrl = _baseUrl;
    _dio.options.headers = {'Content-Type': 'application/json'};
  }

  // Member API methods
  Future<List<Member>> getMembers() async {
    try {
      final response = await _dio.get('members/');
      return (response.data as List)
          .map((json) => Member.fromJson(json))
          .toList();
    } on DioException catch (e) {
      throw Exception('Failed to load members: ${e.message}');
    }
  }

  Future<Member> getMember(String phoneNumber) async {
    try {
      final response = await _dio.get('members/$phoneNumber/');
      return Member.fromJson(response.data);
    } on DioException catch (e) {
      throw Exception('Failed to load member: ${e.message}');
    }
  }
}
