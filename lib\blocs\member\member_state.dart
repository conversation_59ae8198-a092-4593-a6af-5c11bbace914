import 'package:equatable/equatable.dart';
import 'package:gym_management_app/models/member.dart';
import 'package:gym_management_app/models/gym_session.dart';

enum MemberStatus { initial, loading, success, failure }

class MemberState extends Equatable {
  final MemberStatus status;
  final Member? currentUser;
  final List<Member> members;
  final List<Member> membersInGym;
  final List<GymSession> sessions;
  final String? errorMessage;
  final String? successMessage;

  const MemberState({
    this.status = MemberStatus.initial,
    this.currentUser,
    this.members = const [],
    this.membersInGym = const [],
    this.sessions = const [],
    this.errorMessage,
    this.successMessage,
  });

  MemberState copyWith({
    MemberStatus? status,
    Member? currentUser,
    List<Member>? members,
    List<Member>? membersInGym,
    List<GymSession>? sessions,
    String? errorMessage,
    String? successMessage,
  }) {
    return MemberState(
      status: status ?? this.status,
      currentUser: currentUser ?? this.currentUser,
      members: members ?? this.members,
      membersInGym: membersInGym ?? this.membersInGym,
      sessions: sessions ?? this.sessions,
      errorMessage: errorMessage,
      successMessage: successMessage,
    );
  }

  @override
  List<Object?> get props => [
        status,
        currentUser,
        members,
        membersInGym,
        sessions,
        errorMessage,
        successMessage,
      ];
}