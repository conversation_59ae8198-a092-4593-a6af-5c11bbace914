import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gym_management_app/blocs/auth/auth_bloc.dart';
import 'package:gym_management_app/blocs/auth/auth_event.dart';
import 'package:gym_management_app/blocs/member/member_bloc.dart';
import 'package:gym_management_app/blocs/member/member_event.dart';
import 'package:gym_management_app/blocs/member/member_state.dart';
import 'package:gym_management_app/repositories/member_repository.dart';
import 'package:gym_management_app/models/member.dart';
import 'package:gym_management_app/models/gym_session.dart';
import 'package:intl/intl.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  late MemberBloc _memberBloc;

  @override
  void initState() {
    super.initState();
    _memberBloc = MemberBloc(
      memberRepository: MemberRepository(
        authRepository: context.read<AuthBloc>().authRepository,
      ),
    );
    _memberBloc.add(LoadCurrentUser());
  }

  @override
  void dispose() {
    _memberBloc.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Gym Profile'),
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () {
              context.read<AuthBloc>().add(LogoutRequested());
            },
          ),
        ],
      ),
      body: BlocProvider.value(
        value: _memberBloc,
        child: BlocBuilder<MemberBloc, MemberState>(
          builder: (context, state) {
            if (state is MemberLoading) {
              return const Center(child: CircularProgressIndicator());
            } else if (state is MemberError) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text('Error: ${state.message}'),
                    ElevatedButton(
                      onPressed: () => _memberBloc.add(LoadCurrentUser()),
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              );
            } else if (state is CurrentUserLoaded) {
              return _buildUserProfile(state.member, state.sessions);
            }
            return const Center(child: Text('Loading...'));
          },
        ),
      ),
    );
  }

  Widget _buildUserProfile(Member member, List<GymSession> sessions) {
    final dateFormat = DateFormat('MMM dd, yyyy');

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // User Info Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    member.name,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      const Icon(Icons.calendar_today, size: 20),
                      const SizedBox(width: 8),
                      Text('Subscription Start: ${dateFormat.format(member.subscriptionStart)}'),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      const Icon(Icons.event, size: 20),
                      const SizedBox(width: 8),
                      Text('Subscription End: ${dateFormat.format(member.subscriptionEnd)}'),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Currently in Gym?',
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                      ),
                      Switch(
                        value: member.isInGym,
                        onChanged: (value) {
                          if (value) {
                            _memberBloc.add(RecordMemberEntry(id: member.id));
                          } else {
                            _memberBloc.add(RecordMemberExit(id: member.id));
                          }
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Sessions History
          Text(
            'Previous Sessions',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          if (sessions.isEmpty)
            const Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Center(
                  child: Text('No gym sessions yet'),
                ),
              ),
            )
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: sessions.length,
              itemBuilder: (context, index) {
                final session = sessions[index];
                final entryTime = DateFormat('MMM dd, yyyy - HH:mm').format(session.entryTime);
                final exitTime = DateFormat('HH:mm').format(session.exitTime);
                final duration = session.duration.toString();

                return Card(
                  child: ListTile(
                    leading: const Icon(Icons.fitness_center),
                    title: Text(entryTime),
                    subtitle: Text('Exit: $exitTime • Duration: $duration'),
                    trailing: Icon(
                      Icons.check_circle,
                      color: Colors.green[600],
                    ),
                  ),
                );
              },
            ),
        ],
      ),
    );
  }
}