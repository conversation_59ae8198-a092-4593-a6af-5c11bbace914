import 'package:equatable/equatable.dart';

abstract class SessionEvent extends Equatable {
  const SessionEvent();

  @override
  List<Object> get props => [];
}

class LoadUserSessions extends SessionEvent {
  final int userId;

  const LoadUserSessions({required this.userId});

  @override
  List<Object> get props => [userId];
}

class LoadAllSessions extends SessionEvent {}

class RecordEntry extends SessionEvent {
  final int memberId;

  const RecordEntry({required this.memberId});

  @override
  List<Object> get props => [memberId];
}

class RecordExit extends SessionEvent {
  final int memberId;

  const RecordExit({required this.memberId});

  @override
  List<Object> get props => [memberId];
}
