import 'package:equatable/equatable.dart';

class Member extends Equatable {
  final int id;
  final String phoneNumber;
  final String name;
  final bool isInGym;
  final DateTime dateJoined;
  final DateTime subscriptionStart;
  final DateTime subscriptionEnd;
  final bool hasActiveSubscription;

  const Member({
    required this.id,
    required this.phoneNumber,
    required this.name,
    required this.isInGym,
    required this.dateJoined,
    required this.subscriptionStart,
    required this.subscriptionEnd,
    required this.hasActiveSubscription,
  });

  factory Member.fromJson(Map<String, dynamic> json) {
    return Member(
      id: json['id'],
      phoneNumber: json['phone_number'],
      name: json['name'],
      isInGym: json['is_in_gym'],
      dateJoined: DateTime.parse(json['date_joined']),
      subscriptionStart: DateTime.parse(json['subscription_start']),
      subscriptionEnd: DateTime.parse(json['subscription_end']),
      hasActiveSubscription: json['has_active_subscription'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'phone_number': phoneNumber,
      'name': name,
      'is_in_gym': isInGym,
      'date_joined': dateJoined.toIso8601String(),
      'subscription_start': subscriptionStart.toIso8601String(),
      'subscription_end': subscriptionEnd.toIso8601String(),
      'has_active_subscription': hasActiveSubscription,
    };
  }

  @override
  List<Object?> get props => [id, phoneNumber, name, isInGym, dateJoined, subscriptionStart, subscriptionEnd, hasActiveSubscription];
}