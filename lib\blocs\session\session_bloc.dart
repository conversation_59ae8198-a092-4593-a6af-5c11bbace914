import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gym_management_app/blocs/session/session_event.dart';
import 'package:gym_management_app/blocs/session/session_state.dart';
import 'package:gym_management_app/repositories/member_repository.dart';

class SessionBloc extends Bloc<SessionEvent, SessionState> {
  final MemberRepository memberRepository;

  SessionBloc({required this.memberRepository}) : super(const SessionState()) {
    on<LoadUserSessions>(_onLoadUserSessions);
    on<LoadAllSessions>(_onLoadAllSessions);
    on<RecordEntry>(_onRecordEntry);
    on<RecordExit>(_onRecordExit);
  }

  Future<void> _onLoadUserSessions(LoadUserSessions event, Emitter<SessionState> emit) async {
    emit(state.copyWith(status: SessionStatus.loading));
    try {
      final sessions = await memberRepository.getUserSessions(event.userId);
      emit(state.copyWith(
        status: SessionStatus.success,
        sessions: sessions,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: SessionStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onLoadAllSessions(LoadAllSessions event, Emitter<SessionState> emit) async {
    emit(state.copyWith(status: SessionStatus.loading));
    try {
      // This would need a new repository method to get all sessions
      // For now, we'll use an empty list
      emit(state.copyWith(
        status: SessionStatus.success,
        sessions: [],
      ));
    } catch (e) {
      emit(state.copyWith(
        status: SessionStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onRecordEntry(RecordEntry event, Emitter<SessionState> emit) async {
    try {
      await memberRepository.recordMemberEntry(event.memberId);
      emit(state.copyWith(
        successMessage: 'Entry recorded successfully!',
      ));
    } catch (e) {
      emit(state.copyWith(
        status: SessionStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onRecordExit(RecordExit event, Emitter<SessionState> emit) async {
    try {
      await memberRepository.recordMemberExit(event.memberId);
      emit(state.copyWith(
        successMessage: 'Exit recorded successfully!',
      ));
    } catch (e) {
      emit(state.copyWith(
        status: SessionStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }
}
