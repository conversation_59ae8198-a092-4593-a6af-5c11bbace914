import 'package:dio/dio.dart';
import 'package:gym_management_app/models/member.dart';
import 'package:gym_management_app/models/gym_session.dart';
import 'package:gym_management_app/repositories/auth_repository.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';

class MemberRepository {
  static const String _baseUrl = 'https://anaconda2121.pythonanywhere.com/api';
  final AuthRepository authRepository;
  final Dio _dio;

  MemberRepository({required this.authRepository}) : _dio = Dio() {
    _dio.options.baseUrl = _baseUrl;

    // Add pretty logger interceptor
    _dio.interceptors.add(
      PrettyDioLogger(
        requestHeader: true,
        requestBody: true,
        responseBody: true,
        responseHeader: false,
        error: true,
        compact: true,
        maxWidth: 90,
      ),
    );
  }

  Future<void> _setAuthHeaders() async {
    final accessToken = await authRepository.getAccessToken();
    if (accessToken == null) {
      throw Exception('Not authenticated. Please log in.');
    }
    _dio.options.headers = {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $accessToken',
    };
  }

  Future<List<Member>> getMembers() async {
    await _setAuthHeaders();
    try {
      final response = await _dio.get('/members/');

      if (response.statusCode == 200) {
        final List<dynamic> memberJson = response.data;
        return memberJson.map((json) => Member.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load members: ${response.statusCode}');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        await authRepository.refreshToken();
        return getMembers(); // Retry after refreshing token
      }
      throw Exception('Failed to load members: ${e.message}');
    }
  }

  Future<Member> getMemberById(int id) async {
    await _setAuthHeaders();
    try {
      final response = await _dio.get('/members/$id/');

      if (response.statusCode == 200) {
        return Member.fromJson(response.data);
      } else {
        throw Exception('Failed to load member: ${response.statusCode}');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        await authRepository.refreshToken();
        return getMemberById(id); // Retry after refreshing token
      }
      throw Exception('Failed to load member: ${e.message}');
    }
  }

  Future<Member> createMember({
    required String phoneNumber,
    required String password,
    required String name,
  }) async {
    await _setAuthHeaders();
    try {
      final response = await _dio.post(
        '/members/',
        data: {
          'phone_number': phoneNumber,
          'password': password,
          'name': name,
        },
      );

      if (response.statusCode == 201) {
        return Member.fromJson(response.data);
      } else {
        throw Exception('Failed to create member: ${response.statusCode}');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        await authRepository.refreshToken();
        return createMember(phoneNumber: phoneNumber, password: password, name: name);
      }
      throw Exception('Failed to create member: ${e.message}');
    }
  }

  Future<Member> updateMember(int id, {
    String? phoneNumber,
    String? name,
    bool? isInGym,
  }) async {
    await _setAuthHeaders();
    final Map<String, dynamic> body = {};
    if (phoneNumber != null) body['phone_number'] = phoneNumber;
    if (name != null) body['name'] = name;
    if (isInGym != null) body['is_in_gym'] = isInGym;

    try {
      final response = await _dio.patch('/members/$id/', data: body);

      if (response.statusCode == 200) {
        return Member.fromJson(response.data);
      } else {
        throw Exception('Failed to update member: ${response.statusCode}');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        await authRepository.refreshToken();
        return updateMember(id, phoneNumber: phoneNumber, name: name, isInGym: isInGym);
      }
      throw Exception('Failed to update member: ${e.message}');
    }
  }

  Future<void> deleteMember(int id) async {
    await _setAuthHeaders();
    try {
      final response = await _dio.delete('/members/$id/');

      if (response.statusCode != 204) {
        throw Exception('Failed to delete member: ${response.statusCode}');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        await authRepository.refreshToken();
        return deleteMember(id);
      }
      throw Exception('Failed to delete member: ${e.message}');
    }
  }

  Future<void> recordMemberEntry(int id) async {
    await _setAuthHeaders();
    try {
      final response = await _dio.post('/members/$id/enter/');

      if (response.statusCode != 200) {
        throw Exception('Failed to record member entry: ${response.statusCode}');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        await authRepository.refreshToken();
        return recordMemberEntry(id);
      }
      throw Exception('Failed to record member entry: ${e.message}');
    }
  }

  Future<void> recordMemberExit(int id) async {
    await _setAuthHeaders();
    try {
      final response = await _dio.post('/members/$id/exit/');

      if (response.statusCode != 200) {
        throw Exception('Failed to record member exit: ${response.statusCode}');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        await authRepository.refreshToken();
        return recordMemberExit(id);
      }
      throw Exception('Failed to record member exit: ${e.message}');
    }
  }

  Future<Member> getCurrentUser() async {
    // Since there's no /members/me/ endpoint, we need to get the user ID from stored data
    // For now, we'll use a workaround - get all members and find the current user
    // In a real implementation, you'd store the user ID from the login response
    await _setAuthHeaders();
    try {
      final response = await _dio.get('/members/');

      if (response.statusCode == 200) {
        final List<dynamic> memberJson = response.data;
        if (memberJson.isNotEmpty) {
          // For simplicity, return the first member (in a real app, you'd identify the current user)
          return Member.fromJson(memberJson.first);
        } else {
          throw Exception('No members found');
        }
      } else {
        throw Exception('Failed to load current user: ${response.statusCode}');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        await authRepository.refreshToken();
        return getCurrentUser();
      }
      throw Exception('Failed to load current user: ${e.message}');
    }
  }

  Future<List<GymSession>> getUserSessions(int userId) async {
    await _setAuthHeaders();
    try {
      final response = await _dio.get('/sessions/');

      if (response.statusCode == 200) {
        final List<dynamic> sessionJson = response.data;
        // Filter sessions for the specific user
        final userSessions = sessionJson
            .where((json) => json['member'] == userId)
            .map((json) => GymSession.fromJson(json))
            .toList();
        return userSessions;
      } else {
        throw Exception('Failed to load user sessions: ${response.statusCode}');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        await authRepository.refreshToken();
        return getUserSessions(userId);
      }
      throw Exception('Failed to load user sessions: ${e.message}');
    }
  }

  Future<List<Member>> getMembersInGym() async {
    await _setAuthHeaders();
    try {
      final response = await _dio.get('/members/in-gym/');

      if (response.statusCode == 200) {
        final List<dynamic> memberJson = response.data;
        return memberJson.map((json) => Member.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load members in gym: ${response.statusCode}');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        await authRepository.refreshToken();
        return getMembersInGym();
      }
      throw Exception('Failed to load members in gym: ${e.message}');
    }
  }
}
