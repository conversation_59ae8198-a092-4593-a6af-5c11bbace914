import 'package:equatable/equatable.dart';
import 'package:gym_management_app/models/gym_session.dart';

enum SessionStatus { initial, loading, success, failure }

class SessionState extends Equatable {
  final SessionStatus status;
  final List<GymSession> sessions;
  final String? errorMessage;
  final String? successMessage;

  const SessionState({
    this.status = SessionStatus.initial,
    this.sessions = const [],
    this.errorMessage,
    this.successMessage,
  });

  SessionState copyWith({
    SessionStatus? status,
    List<GymSession>? sessions,
    String? errorMessage,
    String? successMessage,
  }) {
    return SessionState(
      status: status ?? this.status,
      sessions: sessions ?? this.sessions,
      errorMessage: errorMessage,
      successMessage: successMessage,
    );
  }

  @override
  List<Object?> get props => [
        status,
        sessions,
        errorMessage,
        successMessage,
      ];
}
