import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:gym_management_app/blocs/app_bloc_observer.dart';
import 'package:gym_management_app/blocs/auth/auth_bloc.dart';
import 'package:gym_management_app/blocs/auth/auth_event.dart';
import 'package:gym_management_app/blocs/auth/auth_state.dart';
import 'package:gym_management_app/repositories/auth_repository.dart';
import 'package:gym_management_app/screens/auth/login_screen.dart';
import 'package:gym_management_app/screens/main_navigation_screen.dart';
import 'package:gym_management_app/screens/splash_screen.dart';

void main() {
  Bloc.observer = AppBlocObserver();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return RepositoryProvider(
      create: (context) => AuthRepository(),
      child: BlocProvider(
        create: (context) => AuthBloc(
          authRepository: RepositoryProvider.of<AuthRepository>(context),
        )..add(AppStarted()), // Dispatch AppStarted event when the app starts
        child: MaterialApp(
          title: 'Gym Management App',
          theme: ThemeData(
            colorScheme: ColorScheme.fromSeed(
              seedColor: const Color(0xFF1B5E20), // Dark green
              brightness: Brightness.light,
            ),
            textTheme: GoogleFonts.(),
            appBarTheme: AppBarTheme(
              backgroundColor: const Color(0xFF2E7D32), // Medium dark green
              foregroundColor: Colors.white,
              titleTextStyle: GoogleFonts.roboto(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
            elevatedButtonTheme: ElevatedButtonThemeData(
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF2E7D32),
                foregroundColor: Colors.white,
                textStyle: GoogleFonts.roboto(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            bottomNavigationBarTheme: const BottomNavigationBarThemeData(
              backgroundColor: Color(0xFF1B5E20),
              selectedItemColor: Colors.white,
              unselectedItemColor: Colors.white70,
            ),
          ),
          home: BlocBuilder<AuthBloc, AuthState>(
            builder: (context, state) {
              if (state.status == AuthStatus.authenticated) {
                return const MainNavigationScreen();
              } else if (state.status == AuthStatus.unauthenticated) {
                return LoginScreen();
              } else if (state.status == AuthStatus.loading || state.status == AuthStatus.initial) {
                return const SplashScreen(); // Show splash screen while checking auth status
              }
              return const Center(child: Text('Unknown State')); // Should not happen
            },
          ),
          routes: {
            '/login': (context) => LoginScreen(),
            '/home': (context) => const MainNavigationScreen(),
          },
        ),
      ),
    );
  }
}