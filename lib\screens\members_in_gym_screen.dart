import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gym_management_app/blocs/member/member_bloc.dart';
import 'package:gym_management_app/blocs/member/member_event.dart';
import 'package:gym_management_app/blocs/member/member_state.dart';
import 'package:gym_management_app/models/member.dart';

class MembersInGymScreen extends StatefulWidget {
  const MembersInGymScreen({super.key});

  @override
  State<MembersInGymScreen> createState() => _MembersInGymScreenState();
}

class _MembersInGymScreenState extends State<MembersInGymScreen> {
  @override
  void initState() {
    super.initState();
    context.read<MemberBloc>().add(LoadMembersInGym());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Members in Gym'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<MemberBloc>().add(LoadMembersInGym());
            },
          ),
        ],
      ),
      body: BlocBuilder<MemberBloc, MemberState>(
        builder: (context, state) {
          if (state.status == MemberStatus.loading) {
            return const Center(child: CircularProgressIndicator());
          } else if (state.status == MemberStatus.failure) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text('Error: ${state.errorMessage}'),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => context.read<MemberBloc>().add(LoadMembersInGym()),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          } else if (state.membersInGym.isEmpty) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.fitness_center,
                    size: 64,
                    color: Colors.grey,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'No members currently in the gym',
                    style: TextStyle(fontSize: 18, color: Colors.grey),
                  ),
                ],
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: () async {
              context.read<MemberBloc>().add(LoadMembersInGym());
            },
            child: ListView.builder(
              padding: const EdgeInsets.all(16.0),
              itemCount: state.membersInGym.length,
              itemBuilder: (context, index) {
                final member = state.membersInGym[index];
                return _buildMemberCard(member);
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildMemberCard(Member member) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12.0),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Colors.green,
          child: Text(
            member.name.isNotEmpty ? member.name[0].toUpperCase() : 'M',
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          member.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Phone: ${member.phoneNumber}'),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  member.hasActiveSubscription 
                    ? Icons.check_circle 
                    : Icons.warning,
                  size: 16,
                  color: member.hasActiveSubscription 
                    ? Colors.green 
                    : Colors.orange,
                ),
                const SizedBox(width: 4),
                Text(
                  member.hasActiveSubscription 
                    ? 'Active Subscription' 
                    : 'Inactive Subscription',
                  style: TextStyle(
                    color: member.hasActiveSubscription 
                      ? Colors.green 
                      : Colors.orange,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: const Icon(
          Icons.fitness_center,
          color: Colors.green,
        ),
      ),
    );
  }
}
