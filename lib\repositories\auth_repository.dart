import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AuthRepository {
  static const String _baseUrl = 'https://anaconda2121.pythonanywhere.com/';
  static const String _accessTokenKey = 'access_token';
  static const String _refreshTokenKey = 'refresh_token';
  
  final Dio _dio;
  
  AuthRepository() : _dio = Dio() {
    _dio.options.baseUrl = _baseUrl;
    _dio.options.headers = {'Content-Type': 'application/json'};
  }

  Future<void> register({
    required String phoneNumber,
    required String password,
    required String name,
    required String email,
  }) async {
    try {
      final response = await _dio.post(
        'api/register/',
        data: {
          'phone_number': phoneNumber,
          'password': password,
          'name': name,
          'email': email,
        },
      );

      if (response.statusCode != 201) {
        throw Exception(response.data['detail'] ?? 'Failed to register');
      }
    } on DioException catch (e) {
      throw Exception(e.response?.data?['detail'] ?? 'Failed to register: ${e.message}');
    }
  }

  Future<void> login({
    required String phoneNumber,
    required String password,
  }) async {
    try {
      final response = await _dio.post(
        'api/login/',
        data: {
          'phone_number': phoneNumber,
          'password': password,
        },
      );

      if (response.statusCode == 200) {
        await _saveTokens(response.data['access'], response.data['refresh']);
      }
    } on DioException catch (e) {
      throw Exception(e.response?.data?['detail'] ?? 'Failed to login: ${e.message}');
    }
  }

  Future<String?> getAccessToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_accessTokenKey);
  }

  Future<String?> getRefreshToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_refreshTokenKey);
  }

  Future<void> _saveTokens(String accessToken, String refreshToken) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_accessTokenKey, accessToken);
    await prefs.setString(_refreshTokenKey, refreshToken);
  }

  Future<void> deleteTokens() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_accessTokenKey);
    await prefs.remove(_refreshTokenKey);
  }

  Future<bool> isAuthenticated() async {
    final accessToken = await getAccessToken();
    return accessToken != null;
  }

  Future<void> refreshToken() async {
    final refreshToken = await getRefreshToken();
    if (refreshToken == null) {
      throw Exception('No refresh token available');
    }

    try {
      final response = await _dio.post(
        'api/token/refresh/',
        data: {'refresh': refreshToken},
      );

      if (response.statusCode == 200) {
        await _saveTokens(response.data['access'], refreshToken); // Keep the same refresh token
      }
    } on DioException catch (e) {
      await deleteTokens(); // Clear tokens if refresh fails
      throw Exception('Failed to refresh token: ${e.message}');
    }
  }
}
