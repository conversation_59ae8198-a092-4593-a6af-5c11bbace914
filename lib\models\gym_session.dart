class GymSession {
  final int? id;
  final int member;
  final String memberName;
  final DateTime entryTime;
  final DateTime exitTime;
  final double duration;

  GymSession({
    this.id,
    required this.member,
    required this.memberName,
    required this.entryTime,
    required this.exitTime,
    required this.duration,
  });

  factory GymSession.fromJson(Map<String, dynamic> json) {
    return GymSession(
      id: json['id'],
      member: json['member'] ?? 0,
      memberName: json['member_name'] ?? 'Unknown',
      entryTime: DateTime.parse(json['entry_time']),
      exitTime: DateTime.parse(json['exit_time']),
      duration: json['duration'] ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'member': member,
      'member_name': memberName,
      'entry_time': entryTime.toIso8601String(),
      'exit_time': exitTime.toIso8601String(),
      'duration': duration,
    };
  }
}
