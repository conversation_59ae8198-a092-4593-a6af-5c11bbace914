class GymSession {
  final int? id;
  final int member;
  final String memberName;
  final DateTime entryTime;
  final DateTime exitTime;
  final String duration;

  GymSession({
    this.id,
    required this.member,
    required this.memberName,
    required this.entryTime,
    required this.exitTime,
    required this.duration,
  });

  factory GymSession.fromJson(Map<String, dynamic> json) {
    return GymSession(
      id: json['id'],
      member: json['member'],
      memberName: json['member_name'],
      entryTime: DateTime.parse(json['entry_time']),
      exitTime: DateTime.parse(json['exit_time']),
      duration: json['duration'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'member': member,
      'member_name': memberName,
      'entry_time': entryTime.toIso8601String(),
      'exit_time': exitTime.toIso8601String(),
      'duration': duration,
    };
  }
}
