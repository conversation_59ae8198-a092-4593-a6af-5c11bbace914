import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gym_management_app/blocs/member/member_event.dart';
import 'package:gym_management_app/blocs/member/member_state.dart';
import 'package:gym_management_app/repositories/member_repository.dart';

class MemberBloc extends Bloc<MemberEvent, MemberState> {
  final MemberRepository memberRepository;

  MemberBloc({required this.memberRepository}) : super(const MemberState()) {
    on<LoadMembers>(_onLoadMembers);
    on<LoadCurrentUser>(_onLoadCurrentUser);
    on<LoadMembersInGym>(_onLoadMembersInGym);
    on<AddMember>(_onAddMember);
    on<UpdateMember>(_onUpdateMember);
    on<DeleteMember>(_onDeleteMember);
    on<RecordMemberEntry>(_onRecordMemberEntry);
    on<RecordMemberExit>(_onRecordMemberExit);
  }

  Future<void> _onLoadMembers(LoadMembers event, Emitter<MemberState> emit) async {
    emit(state.copyWith(status: MemberStatus.loading));
    try {
      final members = await memberRepository.getMembers();
      emit(state.copyWith(
        status: MemberStatus.success,
        members: members,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: MemberStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onLoadCurrentUser(LoadCurrentUser event, Emitter<MemberState> emit) async {
    emit(state.copyWith(status: MemberStatus.loading));
    try {
      final member = await memberRepository.getCurrentUser();
      final sessions = await memberRepository.getUserSessions(member.id);
      emit(state.copyWith(
        status: MemberStatus.success,
        currentUser: member,
        sessions: sessions,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: MemberStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onLoadMembersInGym(LoadMembersInGym event, Emitter<MemberState> emit) async {
    emit(state.copyWith(status: MemberStatus.loading));
    try {
      final membersInGym = await memberRepository.getMembersInGym();
      emit(state.copyWith(
        status: MemberStatus.success,
        membersInGym: membersInGym,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: MemberStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onAddMember(AddMember event, Emitter<MemberState> emit) async {
    emit(state.copyWith(status: MemberStatus.loading));
    try {
      await memberRepository.createMember(
        phoneNumber: event.phoneNumber,
        password: event.password,
        name: event.name,
      );
      emit(state.copyWith(
        status: MemberStatus.success,
        successMessage: 'Member added successfully!',
      ));
      add(LoadMembers()); // Reload members after adding
    } catch (e) {
      emit(state.copyWith(
        status: MemberStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onUpdateMember(UpdateMember event, Emitter<MemberState> emit) async {
    emit(state.copyWith(status: MemberStatus.loading));
    try {
      await memberRepository.updateMember(
        event.id,
        phoneNumber: event.phoneNumber,
        name: event.name,
        isInGym: event.isInGym,
      );
      emit(state.copyWith(
        status: MemberStatus.success,
        successMessage: 'Member updated successfully!',
      ));
      add(LoadMembers()); // Reload members after updating
    } catch (e) {
      emit(state.copyWith(
        status: MemberStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onDeleteMember(DeleteMember event, Emitter<MemberState> emit) async {
    emit(state.copyWith(status: MemberStatus.loading));
    try {
      await memberRepository.deleteMember(event.id);
      emit(state.copyWith(
        status: MemberStatus.success,
        successMessage: 'Member deleted successfully!',
      ));
      add(LoadMembers()); // Reload members after deleting
    } catch (e) {
      emit(state.copyWith(
        status: MemberStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onRecordMemberEntry(RecordMemberEntry event, Emitter<MemberState> emit) async {
    try {
      await memberRepository.recordMemberEntry(event.id);
      emit(state.copyWith(
        successMessage: 'Member entered gym!',
      ));
      add(LoadCurrentUser()); // Reload current user to update status
    } catch (e) {
      emit(state.copyWith(
        status: MemberStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> _onRecordMemberExit(RecordMemberExit event, Emitter<MemberState> emit) async {
    try {
      await memberRepository.recordMemberExit(event.id);
      emit(state.copyWith(
        successMessage: 'Member exited gym!',
      ));
      add(LoadCurrentUser()); // Reload current user to update status
    } catch (e) {
      emit(state.copyWith(
        status: MemberStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }
}